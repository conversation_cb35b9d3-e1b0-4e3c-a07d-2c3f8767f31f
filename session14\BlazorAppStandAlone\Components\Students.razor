﻿@page "/students"

@namespace BlazorAppStandAlone.Components

<h3>Students</h3>

<EditForm Model="@student" OnValidSubmit="@HandleValidSubmit">
    <DataAnnotationsValidator />

    <div class="mb-3">
        <label for="name" class="form-label">Name:</label>
        <InputText class="form-control" @bind-Value="@student.Name" />
        <ValidationMessage For="@(() => student.Name)" />
    </div>
    <div class="mb-3">
        <label for="name" class="form-label">Mobile:</label>
        <InputText class="form-control" @bind-Value="@student.Mobile" />
        <ValidationMessage For="@(() => student.Mobile)" />
    </div>
    <div class="mb-3">
        <label class="form-label">Telephone:</label>
        <InputText class="form-control" @bind-Value="@student.Telephone" />
        <ValidationMessage For="@(() => student.Telephone)" />
    </div>
    <div class="mb-3">
        <label for="email" class="form-label">Email:</label>
        <InputText type="email" class="form-control" @bind-Value="@student.Email" />
        <ValidationMessage For="@(() => student.Email)" />
    </div>

    <div class="mb-3">
        <label class="form-label">Age:</label>
        <InputNumber class="form-control" @bind-Value="@student.Age" />
        <ValidationMessage For="@(() => student.Age)" />
    </div>

    <div class="mb-3">
        <label for="message" class="form-label">Message:</label>
        <InputText class="form-control" @bind-Value="@student.Message" />
        <ValidationMessage For="@(() => student.Message)" />
    </div>

    <ValidationSummary />

    <button type="submit" class="btn btn-primary">Submit</button>
</EditForm>

<h1>Registered Students</h1>
<div class="container mt-5">
    <h2>Bootstrap Table</h2>
    <table class="table table-striped table-bordered">
        <thead class="table-dark">
            <tr>
                <th scope="col">Name</th>
                <th scope="col">Mobile</th>
                <th scope="col">Telephone</th>
                <th scope="col">Email</th>
                <th scope="col">Age</th>
                <th scope="col">Message</th>
                <th scope="col">Operations</th>
            </tr>
        </thead>
        <tbody>
            @foreach (Student item in students)
            {
                <tr>
                    <td scope="col">@item.Name</td>
                    <td scope="col">@item.Mobile</td>
                    <td scope="col">@item.Telephone</td>
                    <td scope="col">@item.Email</td>
                    <td scope="col">@item.Age</td>
                    <td scope="col">@item.Message</td>
                    <td scope="col"><a href="javascript:void(0)" onclick="@( ()=> EditStudent(item) )">Edit</a></td>
                </tr>
            }
        </tbody>
    </table>
</div>